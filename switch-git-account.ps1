# Git Account Switcher Script
# Usage: .\switch-git-account.ps1 [main|student]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("main", "student")]
    [string]$Account
)

if ($Account -eq "main") {
    Write-Host "Switching to MAIN GitHub account..." -ForegroundColor Green
    git config --global user.name "rayenchanchah"
    git config --global user.email "<EMAIL>"
    Write-Host "✓ Configured for main account" -ForegroundColor Green
    Write-Host "  Name: rayenchanchah" -ForegroundColor Yellow
    Write-Host "  Email: <EMAIL>" -ForegroundColor Yellow
    Write-Host "  SSH Host: github.com" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To clone repositories from main account:" -ForegroundColor Cyan
    Write-Host "  <NAME_EMAIL>:rayenchanchah/repository-name.git" -ForegroundColor White
}
elseif ($Account -eq "student") {
    Write-Host "Switching to STUDENT GitHub account..." -ForegroundColor Blue
    git config --global user.name "rayenchanchah"
    git config --global user.email "<EMAIL>"
    Write-Host "✓ Configured for student account" -ForegroundColor Green
    Write-Host "  Name: rayenchanchah" -ForegroundColor Yellow
    Write-Host "  Email: <EMAIL>" -ForegroundColor Yellow
    Write-Host "  SSH Host: github-student.com" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To clone repositories from student account:" -ForegroundColor Cyan
    Write-Host "  <NAME_EMAIL>:rayenchanchah/repository-name.git" -ForegroundColor White
}

Write-Host ""
Write-Host "Current Git configuration:" -ForegroundColor Magenta
git config --global user.name
git config --global user.email
